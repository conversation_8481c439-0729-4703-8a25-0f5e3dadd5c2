'use client'

import React, { useState, useEffect, useRef, useCallback } from 'react'
import { useAuth } from '@/app/contexts/AuthContext'
import { useSettings } from './SettingsContext'
import { paypalConfig } from '@/lib/paypal/config'
import { CheckIcon, StarIcon, SparklesIcon } from '@heroicons/react/20/solid'
import { XMarkIcon, CreditCardIcon, ClockIcon, ArrowPathIcon, ShieldCheckIcon, ExclamationTriangleIcon } from '@heroicons/react/24/outline'
import { Dialog, Transition } from '@headlessui/react'
import { Fragment } from 'react'
import { db } from '@/lib/firebase/config';
import { doc, updateDoc, getDoc, collection, query, where, getDocs, onSnapshot, limit } from 'firebase/firestore';
import { toast } from 'react-hot-toast';
import { PayPalSubscription } from '@/lib/paypal/types';
import ModalPortal from '../customize/ModalPortal';

export default function BillingSection() {
  // Contexto de autenticación
  const { user, subscriptionStatus, refreshSubscription } = useAuth()
  // Contexto de settings que incluye información de la suscripción
  const { subscription } = useSettings()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [billingCycle, setBillingCycle] = useState<'monthly' | 'yearly'>('monthly')
  // Mantenemos el estado de suscripción original
  const [paypalSubscription, setPaypalSubscription] = useState<PayPalSubscription | null>(null)
  const [plans, setPlans] = useState<any[]>([])
  const [plansLoading, setPlansLoading] = useState(true)
  const [showRenewalModal, setShowRenewalModal] = useState(false)

  const [isPro, setIsPro] = useState(false)
  const [showCancelModal, setShowCancelModal] = useState(false)

  const [isCancelling, setIsCancelling] = useState(false)
  const [cancelError, setCancelError] = useState('')
  const [showPaymentOptions, setShowPaymentOptions] = useState(false)
  const cancelReasonRef = useRef(null)
  const [isCancelled, setIsCancelled] = useState(false)
  const [endDate, setEndDate] = useState<string | null>(null)
  const [isSuspended, setIsSuspended] = useState(false)
  // Nuevos estados para cancelación programada
  const [isScheduledForCancellation, setIsScheduledForCancellation] = useState(false)
  const [scheduledCancellationDate, setScheduledCancellationDate] = useState<string | null>(null)
  // Agregar estado para debug
  const [debugInfo, setDebugInfo] = useState({
    firestoreData: null as any,
    isSuspendedState: false
  });
  // Añadir estado para almacenar información adicional
  const [nextRenewalDate, setNextRenewalDate] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<string | null>(null);

  // Función helper para detectar el ciclo de facturación actual
  const getCurrentBillingCycle = () => {
    // Si hay datos de suscripción, intentar detectar el ciclo basado en el plan ID o precio
    if (debugInfo.firestoreData?.subscriptionPlanId) {
      const planId = debugInfo.firestoreData.subscriptionPlanId;
      // Verificar que planId sea una string antes de usar includes
      if (typeof planId === 'string') {
        // Lógica para detectar si es mensual o anual basado en el plan ID
        if (planId.includes('yearly') || planId.includes('annual')) {
          return 'yearly';
        } else if (planId.includes('monthly')) {
          return 'monthly';
        }
      }
    }
    // Fallback al estado local
    return billingCycle;
  };

  // Función helper para detectar el tipo de plan
  const getCurrentPlanType = () => {
    // TODO: Implementar detección del plan "Estudio" cuando esté disponible
    //
    // Cuando implementes el plan "Estudio", puedes usar algo como:
    // if (debugInfo.firestoreData?.planType === 'studio') return 'Estudio';
    // if (debugInfo.firestoreData?.planType === 'artist') return 'Artista del Tatuaje';
    //
    // O basado en el número de cuentas:
    // if (debugInfo.firestoreData?.accountCount > 1) return 'Estudio';
    //
    // O basado en el plan ID:
    // if (debugInfo.firestoreData?.subscriptionPlanId?.includes('studio')) return 'Estudio';

    return "Artista del Tatuaje"; // Por defecto hasta que se implemente "Estudio"
  };

  // Planes de Reveniu
  const reveniuPlans = {
    regular: {
      monthly: "14875", // Plan Pro mensual (ID)
      yearly: "14876"   // Plan Pro anual (ID)
    },
    lanzamiento: "14877", // Plan Pro (Lanzamiento) (ID)
    ganador: "14878",    // Plan Pro (Ganador Sorteo) (ID)
    especial: "14879",   // Plan Pro (Especial) (ID)
    tmf: "14880"         // Plan Pro (TMF) (ID)
  };

  // Obtener el estado de suscripción del usuario desde Firestore
  const [hadSubscriptionBefore, setHadSubscriptionBefore] = useState(false);
  // Estado para rastrear el proveedor de pago (paypal o reveniu)
  const [paymentProvider, setPaymentProvider] = useState<string | null>(null);

  // La sección de useEffect original que carga los planes
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        setPlansLoading(true);

        // Definimos los planes directamente ya que PayPal no tiene una API para obtener planes
        const defaultPlans = [
          {
            id: 'free',
            name: 'Plan Tatuador Independiente',
            description: 'Funcionalidades básicas para comenzar',
            monthlyPrice: 0,
            yearlyPrice: 0,
            features: [
              'Perfil básico de tatuador',
              'Hasta 10 imágenes en portafolio',
              'Enlace público a tu perfil',
              'Galería básica',
              'Soporte por email',
            ],
            recommended: false
          },
          {
            id: 'pro',
            name: 'Plan Artista del Tatuaje',
            description: 'Todo lo que necesitas para tu estudio de tatuajes',
            monthlyPrice: 50, // USD
            yearlyPrice: 480, // USD (redondeado)
            features: [
              'Todo lo del plan gratuito',
              'Perfil verificado',
              'Portafolio ilimitado',
              'Mensajería integrada con clientes',
              'Integración con Instagram y Facebook',
              'Calendario de citas',
              'Notificaciones personalizadas',
              'Gestión de clientes',
              'Estadísticas avanzadas',
              'Posicionamiento preferencial en búsquedas',
              'Gestión de facturación',
              'Soporte prioritario 24/7',
            ],
            recommended: true
          }
        ];

        setPlans(defaultPlans);
      } catch (err) {
        console.error('Error fetching plans:', err);
        setError('Error al cargar los planes');
      } finally {
        setPlansLoading(false);
      }
    };

    fetchPlans();
  }, [user]);

  // Simplificar la función checkSubscription para evitar bucles
  const checkSubscription = async () => {
    if (!user?.email || !user?.uid) return;
    await refreshSubscription();
  };

  // Función simple para abrir el modal de renovación
  const openRenewalModal = () => {
    setShowRenewalModal(true);
  };

  // Función simple para cerrar el modal de renovación
  const closeRenewalModal = () => {
    setShowRenewalModal(false);
  };



  // Definir interfaces para los planes
  interface PlanFeature {
    id: string;
    name: string;
  }

  interface Plan {
    id: string;
    name: string;
    description: string;
    monthlyPrice: number;
    yearlyPrice: number;
    features: string[];
    recommended: boolean;
  }

  // Obtener el plan gratuito y el plan Pro
  const freePlan = plans.find((plan: Plan) => plan.id === 'free');
  const proPlan = plans.find((plan: Plan) => plan.id === 'pro');

  // Usar las características de los planes desde la API si están disponibles
  const freeFeatures = [
    { name: 'Estudio', enabled: true },
    { name: 'Calendario', enabled: true },
    { name: 'Mensajes', enabled: false },
    { name: 'Finanzas', enabled: false },
    { name: 'Clientes', enabled: false },
    { name: 'Portfolio', enabled: true },
    { name: 'Reseñas', enabled: true },
    { name: 'Automatizaciones', enabled: false },
    { name: 'Análisis', enabled: false },
    { name: 'Personalización Sitio', enabled: true, description: 'Básica (logo y nombre)' },
    { name: 'Enlaces', enabled: true, description: 'Agregar enlaces básicos' },
    { name: 'Asistente de IA', enabled: false },
    { name: 'Soporte', enabled: false }
  ];

  const proFeatures = [
    { name: 'Estudio', enabled: true },
    { name: 'Calendario', enabled: true },
    { name: 'Mensajes', enabled: true },
    { name: 'Finanzas', enabled: true },
    { name: 'Clientes', enabled: true },
    { name: 'Portfolio', enabled: true },
    { name: 'Reseñas', enabled: true },
    { name: 'Automatizaciones', enabled: true },
    { name: 'Análisis', enabled: true },
    { name: 'Personalización Sitio', enabled: true, description: 'Completa (branding, estilos personalizados)' },
    { name: 'Enlaces', enabled: true, description: 'Enlaces avanzados (configuración personalizada)' },
    { name: 'Asistente de IA', enabled: true, description: 'Incluido (respuesta automatizada y manejo de consultas)' },
    { name: 'Soporte', enabled: true }
  ];





  // Función para obtener el ID de suscripción del usuario y el proveedor desde las diferentes colecciones
  const getUserSubscriptionId = async (): Promise<{id: string, provider: string} | null> => {
    try {
      if (!user) {
        toast.error('No hay un usuario autenticado');
        return null;
      }

      console.log('🔎 Buscando ID de suscripción para usuario:', user.uid);

      // 1. Verificar en documento de usuario
      try {
        const userDoc = await getDoc(doc(db, 'users', user.uid));
        if (userDoc.exists()) {
          const userData = userDoc.data();

          // Buscar PayPal primero
          if (userData.paypalSubscriptionId) {
            console.log('✅ ID de suscripción PayPal encontrado:', userData.paypalSubscriptionId);
            return { id: userData.paypalSubscriptionId, provider: 'paypal' };
          }

          // Buscar Reveniu
          if (userData.reveniuSubscriptionId) {
            console.log('✅ ID de suscripción Reveniu encontrado:', userData.reveniuSubscriptionId);
            return { id: userData.reveniuSubscriptionId, provider: 'reveniu' };
          }

          // Campo genérico - intentar determinar el proveedor por el formato del ID
          if (userData.subscriptionId) {
            console.log('✅ ID de suscripción genérico encontrado:', userData.subscriptionId);
            // Los IDs de PayPal tienen formato como "I-BW452GLLEP1G"
            // Los IDs de Reveniu son números como "191196"
            const isPayPalFormat = /^I-[A-Z0-9]+$/.test(userData.subscriptionId);
            const provider = isPayPalFormat ? 'paypal' : 'reveniu';
            console.log(`🔍 Detectado proveedor por formato de ID: ${provider}`);
            return { id: userData.subscriptionId, provider };
          }
        } else {
          console.log('❌ No se encontró documento de usuario');
        }
      } catch (e) {
        console.error('❌ Error al obtener documento de usuario:', e);
      }

      // 2. Verificar en la colección de suscripciones
      try {
        const subDoc = await getDoc(doc(db, 'subscriptions', user.uid));
        if (subDoc.exists()) {
          const subData = subDoc.data();

          if (subData.paypalSubscriptionId) {
            console.log('✅ ID de suscripción PayPal encontrado en colección de suscripciones:', subData.paypalSubscriptionId);
            return { id: subData.paypalSubscriptionId, provider: 'paypal' };
          }

          if (subData.reveniuSubscriptionId) {
            console.log('✅ ID de suscripción Reveniu encontrado en colección de suscripciones:', subData.reveniuSubscriptionId);
            return { id: subData.reveniuSubscriptionId, provider: 'reveniu' };
          }

          if (subData.subscriptionId) {
            console.log('✅ ID de suscripción genérico encontrado en colección de suscripciones:', subData.subscriptionId);
            const isPayPalFormat = /^I-[A-Z0-9]+$/.test(subData.subscriptionId);
            const provider = isPayPalFormat ? 'paypal' : 'reveniu';
            console.log(`🔍 Detectado proveedor por formato de ID: ${provider}`);
            return { id: subData.subscriptionId, provider };
          }
        } else {
          console.log('❌ No se encontró documento en colección de suscripciones');
        }
      } catch (e) {
        console.error('❌ Error al obtener documento de suscripciones:', e);
      }

      // 3. Buscar en mappings
      try {
        const mappingsCol = collection(db, 'subscription_mappings');
        const q = query(mappingsCol, where('userId', '==', user.uid), limit(1));
        const mappingsSnapshot = await getDocs(q);

        if (!mappingsSnapshot.empty) {
          const mapping = mappingsSnapshot.docs[0];
          const mappingData = mapping.data();

          // El ID del documento puede ser el ID de suscripción
          if (mapping.id) {
            console.log('✅ ID de suscripción encontrado en mapping (doc ID):', mapping.id);
            const isPayPalFormat = /^I-[A-Z0-9]+$/.test(mapping.id);
            const provider = isPayPalFormat ? 'paypal' : 'reveniu';
            console.log(`🔍 Detectado proveedor por formato de ID: ${provider}`);
            return { id: mapping.id, provider };
          }

          // O puede estar en un campo dentro del documento
          if (mappingData.subscriptionId) {
            console.log('✅ ID de suscripción encontrado en mapping (campo):', mappingData.subscriptionId);
            const isPayPalFormat = /^I-[A-Z0-9]+$/.test(mappingData.subscriptionId);
            const provider = isPayPalFormat ? 'paypal' : 'reveniu';
            console.log(`🔍 Detectado proveedor por formato de ID: ${provider}`);
            return { id: mappingData.subscriptionId, provider };
          }

          if (mappingData.providerSubscriptionId) {
            console.log('✅ ID de suscripción encontrado en mapping (providerSubscriptionId):', mappingData.providerSubscriptionId);
            const isPayPalFormat = /^I-[A-Z0-9]+$/.test(mappingData.providerSubscriptionId);
            const provider = isPayPalFormat ? 'paypal' : 'reveniu';
            console.log(`🔍 Detectado proveedor por formato de ID: ${provider}`);
            return { id: mappingData.providerSubscriptionId, provider };
          }
        } else {
          console.log('❌ No se encontraron mappings para el usuario');
        }
      } catch (e) {
        console.error('❌ Error al buscar en mappings:', e);
      }

      console.error('❌ No se encontró ID de suscripción en ninguna colección');

      // Mostrar mensaje específico al usuario
      toast.error('No se pudo encontrar información de tu suscripción. Por favor, contacta a soporte para asistencia.',
        { duration: 6000 }
      );

      return null;

    } catch (e) {
      console.error('❌ Error al buscar ID de suscripción:', e);
      toast.error('Error al recuperar información de suscripción. Por favor, intenta nuevamente más tarde o contacta a soporte.',
        { duration: 6000 }
      );
      return null;
    }
  };

  // Función para abrir el customer portal de Paddle
  const handleCancelSubscription = async () => {
    setIsCancelling(true);
    setCancelError('');

    try {
      if (!user?.uid) {
        throw new Error('No se pudo obtener información del usuario');
      }

      console.log('� Abriendo customer portal de Paddle...');

      // Llamar a nuestra API para crear una sesión de customer portal
      const response = await fetch('/api/paddle/customer-portal', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: user.uid
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('❌ Error al crear customer portal:', data);
        throw new Error(data.error || 'Error al abrir el portal de gestión');
      }

      // Cerrar el modal
      setShowCancelModal(false);

      // Abrir el customer portal en una nueva ventana
      window.open(data.portal_url, '_blank');

      // Mostrar mensaje informativo
      toast.success('Se ha abierto el portal de gestión de Paddle donde puedes cancelar o modificar tu suscripción.', {
        duration: 6000
      });

    } catch (error: any) {
      console.error('Error al abrir customer portal:', error);
      setCancelError(error.message || 'Ocurrió un error al abrir el portal de gestión');
    } finally {
      setIsCancelling(false);
    }
  };



  // Determinar si el usuario ya tiene una suscripción Pro activa
  const userIsPro = subscriptionStatus === 'active';

  // Asegurarnos de que tengamos un listener para cambios en tiempo real del usuario
  useEffect(() => {
    if (!user) return;

    console.log('🔔 Configurando listener para cambios en el documento del usuario');

    const unsubscribe = onSnapshot(doc(db, 'users', user.uid), (userDoc) => {
      if (userDoc.exists()) {
        const userData = userDoc.data();
        console.log('📊 Datos de usuario actualizados por listener:', JSON.stringify(userData, null, 2));

        // Guardar los datos completos para debug
        setDebugInfo(prev => ({
          ...prev,
          firestoreData: userData
        }));

        // Actualizar estados basados en los datos del usuario
        const userIsSuspended = !!userData.subscriptionSuspended;
        console.log('ℹ️ Estado de suspensión del usuario:', userIsSuspended);
        console.log('ℹ️ Fecha de suspensión:', userData.suspendedAt);

        // Determinar el proveedor de pago
        let provider = null;
        if (userData.paypalSubscriptionId) {
          provider = 'paypal';
        } else if (userData.reveniuSubscriptionId) {
          provider = 'reveniu';
        } else if (userData.subscriptionId) {
          // Determinar por formato
          const isPayPalFormat = /^I-[A-Z0-9]+$/.test(userData.subscriptionId);
          provider = isPayPalFormat ? 'paypal' : 'reveniu';
        }
        console.log('ℹ️ Proveedor de pago detectado:', provider);
        setPaymentProvider(provider);
        
        setIsSuspended(userIsSuspended);
        setDebugInfo(prev => ({
          ...prev,
          isSuspendedState: userIsSuspended
        }));

        // Manejar cancelación programada (Paddle)
        console.log('🔍 Verificando cancelación programada:', {
          isScheduledForCancellation: userData.isScheduledForCancellation,
          scheduledCancellationDate: userData.scheduledCancellationDate,
          scheduledChangeAction: userData.scheduledChangeAction
        });

        if (userData.isScheduledForCancellation && userData.scheduledCancellationDate) {
          console.log('✅ Cancelación programada detectada en frontend');
          setIsScheduledForCancellation(true);

          // Formatear la fecha de cancelación programada
          let date;
          if (typeof userData.scheduledCancellationDate.toDate === 'function') {
            // Es un Timestamp de Firestore
            date = userData.scheduledCancellationDate.toDate();
          } else {
            // Es un objeto Date o una fecha en string
            date = new Date(userData.scheduledCancellationDate);
          }
          setScheduledCancellationDate(date.toLocaleDateString('es-ES'));
          console.log('📅 Fecha de cancelación programada formateada:', date.toLocaleDateString('es-ES'));
        } else {
          console.log('❌ No hay cancelación programada detectada');
          setIsScheduledForCancellation(false);
          setScheduledCancellationDate(null);
        }

        // Manejar cancelación final (legacy y Paddle)
        if (userData.subscriptionCancelled && userData.finalAccessDate) {
          setIsCancelled(true);

          // Formatear la fecha de finalización
          let date;
          if (typeof userData.finalAccessDate.toDate === 'function') {
            // Es un Timestamp de Firestore
            date = userData.finalAccessDate.toDate();
          } else {
            // Es un objeto Date o una fecha en string
            date = new Date(userData.finalAccessDate);
          }
          setEndDate(date.toLocaleDateString('es-ES'));
        } else {
          setIsCancelled(false);
        }

        // Obtener la plataforma de pago
        if (userData.paymentProvider) {
          const provider = userData.paymentProvider.toLowerCase();
          setPaymentMethod(provider === 'paypal' ? 'PayPal' : 'Tarjeta bancaria');
        } else if (userData.subscriptionProvider) {
          const provider = userData.subscriptionProvider.toLowerCase();
          setPaymentMethod(provider === 'paypal' ? 'PayPal' : 'Tarjeta bancaria');
        } else {
          setPaymentMethod(null);
        }

        // Obtener fecha de próxima renovación
        if (userData.nextBillingDate) {
          let billingDate;
          if (typeof userData.nextBillingDate.toDate === 'function') {
            billingDate = userData.nextBillingDate.toDate();
          } else {
            billingDate = new Date(userData.nextBillingDate);
          }
          setNextRenewalDate(billingDate.toLocaleDateString());
        } else if (userData.subscriptionEndDate && !userData.subscriptionCancelled) {
          // Si no hay nextBillingDate pero hay subscriptionEndDate y no está cancelada,
          // podemos usar subscriptionEndDate como aproximación
          let billingDate;
          if (typeof userData.subscriptionEndDate.toDate === 'function') {
            billingDate = userData.subscriptionEndDate.toDate();
          } else {
            billingDate = new Date(userData.subscriptionEndDate);
          }
          setNextRenewalDate(billingDate.toLocaleDateString());
        } else {
          setNextRenewalDate(null);
        }
      }
    });

    return () => {
      console.log('🔔 Limpiando listener de usuario');
      unsubscribe();
    };
  }, [user]);

  // Agregar función para recargar datos de usuario
  const refreshUserData = useCallback(async () => {
    if (!user) return;

    try {
      const userDoc = await getDoc(doc(db, 'users', user.uid));
      if (userDoc.exists()) {
        const userData = userDoc.data();

        // Actualizar estados basados en los datos del usuario
        setIsSuspended(!!userData.subscriptionSuspended);
        setIsCancelled(!!userData.subscriptionCancelled);

        if (userData.finalAccessDate) {
          let date;
          if (typeof userData.finalAccessDate.toDate === 'function') {
            // Es un Timestamp de Firestore
            date = userData.finalAccessDate.toDate();
          } else {
            // Es un objeto Date o una fecha en string
            date = new Date(userData.finalAccessDate);
          }
          setEndDate(date.toLocaleDateString());
        }
      }
    } catch (error) {
      console.error('Error refreshing user data:', error);
    }
  }, [user]);

  // Agregar useEffect para refrescar datos periódicamente
  useEffect(() => {
    if (!user) return;

    // Refrescar datos inmediatamente al montar
    refreshUserData();

    // Configurar un intervalo para refrescar datos cada 30 segundos
    const intervalId = setInterval(() => {
      refreshUserData();
    }, 30000);

    // Limpiar el intervalo al desmontar
    return () => clearInterval(intervalId);
  }, [user, refreshUserData]);

  useEffect(() => {
    // Si el usuario cambia su estado de suscripción, actualizar el estado local
    setIsPro(subscriptionStatus === 'active');

    // Actualizar estado de suscripción suspendida basado en el contexto
    if (subscriptionStatus === 'suspended') {
      setIsSuspended(true);
      console.log('✅ Detectada suscripción suspendida desde contexto Auth');
    } else if (subscriptionStatus === 'cancelled') {
      // Si el estado es cancelled, asegurarse de que isCancelled también lo sea
      setIsCancelled(true);
      console.log('✅ Detectada suscripción cancelada desde contexto Auth');
    } else if (subscriptionStatus === 'active') {
      // Si el estado es active desde el contexto, verificar si debemos anular los estados locales
      // Solo lo hacemos si no tenemos información específica de Firestore (isCancelled o isSuspended)
      if (!debugInfo.firestoreData?.subscriptionCancelled && isCancelled) {
        setIsCancelled(false);
      }
      if (!debugInfo.firestoreData?.subscriptionSuspended && isSuspended) {
        setIsSuspended(false);
      }
    }
  }, [subscriptionStatus, isSuspended, isCancelled, debugInfo.firestoreData]);

  // Detectar cambios en los estados locales y mantener consistencia
  useEffect(() => {
    // Este efecto asegura que isPro sea consistente con isSuspended e isCancelled
    if (isSuspended || isCancelled) {
      // Si la suscripción está suspendida o cancelada pero aún tiene acceso,
      // isPro debe ser true para mostrar correctamente la UI
      // Nos basamos en la existencia de endDate para determinar si aún tiene acceso
      if (endDate) {
        const endDateObj = new Date(endDate);
        const now = new Date();
        if (endDateObj > now) {
          // Si la fecha de fin es futura, el usuario aún tiene acceso Pro
          setIsPro(true);
        } else {
          // Si la fecha de fin ya pasó, el usuario debería perder acceso Pro
          setIsPro(false);
        }
      }
    }
  }, [isSuspended, isCancelled, endDate, subscriptionStatus]);

  // Formatear fecha para mostrar
  const formatDate = (date: Date | null) => {
    if (!date) return '';

    return new Intl.DateTimeFormat('es-ES', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    }).format(date);
  };

  // Función para reactivar una suscripción suspendida
  const handleReactivate = async () => {
    if (!user) return;

    try {
      setIsLoading(true);

      const subscriptionInfo = await getUserSubscriptionId();
      if (!subscriptionInfo) {
        toast.error('No se pudo encontrar el ID de suscripción');
        setIsLoading(false);
        return;
      }

      const { id: subscriptionId, provider: paymentProvider } = subscriptionInfo;

      console.log(`📤 Enviando solicitud de reactivación para: ${subscriptionId} (Proveedor: ${paymentProvider})`);

      // Determinar la URL del endpoint según el proveedor
      const endpoint = paymentProvider === 'reveniu' ? '/api/reveniu/reactivate' : '/api/paypal/reactivate';

      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          subscriptionId,
          userId: user.uid,
          // No solicitamos razón al usuario, usamos un valor fijo
          reason: "Reactivating the subscription"
        })
      });

      const data = await response.json();

      if (response.ok) {
        // Refrescar datos del usuario en cualquier caso
        refreshUserData();
        
        // Forzar la actualización del contexto de autenticación para reflejar el cambio de estado
        if (typeof refreshSubscription === 'function') {
          await refreshSubscription();
        }

        if (data.cleaned) {
          // Si se limpió el estado de la suscripción
          toast.success('Se ha limpiado el estado de tu suscripción. Puedes crear una nueva suscripción cuando gustes.', {
            duration: 6000
          });
        } else if (paymentProvider === 'reveniu') {
          // Para Reveniu, verificar qué método se utilizó
          if (data.method === 'extend') {
            // Si se usó el método extend, la suscripción ya está reactivada
            toast.success('Tu suscripción ha sido reactivada exitosamente.', { duration: 5000 });
            setIsSuspended(false); // Actualizamos el estado local inmediatamente
          } else {
            // Si se usó el método change_method, se envió un correo
            toast.success('Se ha enviado un correo electrónico con instrucciones para actualizar tu método de pago.', { duration: 5000 });
            // No cambiamos el estado local todavía, esperamos a que llegue el webhook
          }
        } else {
          // Para PayPal mantenemos el mensaje anterior
          toast.success('Suscripción reactivada exitosamente');
          setIsSuspended(false);
        }
      } else {
        // Manejar errores o respuestas no exitosas
        console.error('Error en la respuesta del backend:', data);

        if (data.notFound) {
          toast.error('La suscripción no existe en el sistema de pagos. Se ha limpiado el estado local.', {
            duration: 6000
          });
          // Recargar para mostrar el estado actualizado
          setTimeout(() => window.location.reload(), 2000);
        } else if (paymentProvider === 'reveniu' && data.method === 'extend_failed') {
          // Caso específico donde la extensión falló en Reveniu
          toast.error(data.error || 'Reveniu no pudo reactivar la suscripción. Intenta actualizar tu método de pago o contacta a soporte.', {
            duration: 8000 // Mensaje más largo, más tiempo para leer
          });
        } else {
          // Error genérico
          toast.error(`Error al reactivar la suscripción: ${data.error || 'Error desconocido'}`);
        }
      }
    } catch (error) {
      console.error('Error reactivating subscription:', error);
      toast.error('Error al reactivar la suscripción');
    } finally {
      setIsLoading(false);
    }
  };

  // Añadir un log en el render para verificar exactamente qué valores tienen las variables
  console.log('🚨 RENDER - Estado actual:', {
    subscriptionStatus,
    isSuspended,
    isCancelled,
    debugInfo
  });

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="space-y-12">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-2">Facturación</h2>
          <p className="text-gray-600">Gestiona tu suscripción y métodos de pago</p>
        </div>





        {/* Mostrar contenido según el tipo de usuario */}
        {!userIsPro ? (
          // Usuario FREE - Mostrar solo tarjetas de precios
          <>
            {/* Toggle de ciclo de facturación - Diseño moderno */}
            <div className="flex justify-center mb-8">
              <div className="bg-gray-100 p-1 rounded-xl inline-flex">
                <button
                  type="button"
                  onClick={() => setBillingCycle('monthly')}
                  className={`px-6 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 ${
                    billingCycle === 'monthly'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Mensual
                </button>
                <button
                  type="button"
                  onClick={() => setBillingCycle('yearly')}
                  className={`px-6 py-2.5 text-sm font-medium rounded-lg transition-all duration-200 flex items-center ${
                    billingCycle === 'yearly'
                      ? 'bg-white text-gray-900 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                >
                  Anual
                  <span className="ml-2 inline-flex items-center rounded-full bg-green-100 px-2 py-0.5 text-xs font-medium text-green-800">
                    -20%
                  </span>
                </button>
              </div>
            </div>

            {/* Planes de precios - Diseño moderno con tarjetas */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 max-w-5xl mx-auto">
          {/* Plan Tatuador Independiente */}
          <div className="relative bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
            <div className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="bg-gray-100 rounded-xl p-3">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">Tatuador Independiente</h3>
                    <p className="text-gray-600 text-sm">Perfecto para comenzar</p>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <div className="flex items-baseline">
                  <span className="text-4xl font-bold text-gray-900">$0</span>
                  <span className="text-gray-500 ml-2">/mes</span>
                </div>
                <p className="text-sm text-gray-600 mt-1">Gratis para siempre</p>
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-semibold text-gray-900 uppercase tracking-wide">Funcionalidades incluidas</h4>
                <ul className="space-y-3">
                  {freeFeatures.filter(f => f.enabled).slice(0, 6).map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <CheckIcon className="h-5 w-5 text-gray-400 mt-0.5 flex-shrink-0" />
                      <span className="ml-3 text-sm text-gray-600">
                        {feature.name}
                        {feature.description && (
                          <span className="text-gray-500 block text-xs mt-0.5">{feature.description}</span>
                        )}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            <div className="px-8 py-6 bg-gray-50 border-t border-gray-200">
              <button
                disabled={true}
                className="w-full py-3 px-4 bg-gray-200 text-gray-600 rounded-xl font-medium cursor-not-allowed"
              >
                Plan Actual
              </button>
            </div>
          </div>

          {/* Plan Artista del Tatuaje */}
          <div className="relative bg-white rounded-2xl border-2 border-black shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
            {/* Etiqueta de recomendado */}
            <div className="absolute top-0 right-0 z-10">
              <div className="bg-black text-white text-xs font-bold px-4 py-2 rounded-bl-xl">
                RECOMENDADO
              </div>
            </div>

            <div className="p-8">
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-3">
                  <div className="bg-black rounded-xl p-3">
                    <SparklesIcon className="h-6 w-6 text-white" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">Artista del Tatuaje</h3>
                    <p className="text-gray-600 text-sm">Solución completa para profesionales</p>
                  </div>
                </div>
              </div>

              <div className="mb-6">
                <div className="flex items-baseline">
                  <span className="text-4xl font-bold text-gray-900">
                    {billingCycle === 'monthly' ? '$50' : '$480'}
                  </span>
                  <span className="text-gray-500 ml-2">
                    {billingCycle === 'monthly' ? '/mes' : '/año'}
                  </span>
                </div>
                {billingCycle === 'yearly' && (
                  <div className="flex items-center mt-2">
                    <span className="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-1 rounded-full">
                      Ahorras $120 (20% descuento)
                    </span>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                <h4 className="text-sm font-semibold text-gray-900 uppercase tracking-wide">Todo incluido</h4>
                <ul className="space-y-3">
                  {proFeatures.filter(f => f.enabled).slice(0, 8).map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <CheckIcon className="h-5 w-5 text-black mt-0.5 flex-shrink-0" />
                      <span className="ml-3 text-sm text-gray-600">
                        {feature.name}
                        {feature.description && (
                          <span className="text-gray-500 block text-xs mt-0.5">{feature.description}</span>
                        )}
                      </span>
                    </li>
                  ))}
                  <li className="flex items-start">
                    <CheckIcon className="h-5 w-5 text-black mt-0.5 flex-shrink-0" />
                    <span className="ml-3 text-sm text-gray-600">
                      Y mucho más...
                    </span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="px-8 py-6 bg-gray-50 border-t border-gray-200">
              {/* Botones con lógica correcta según estado */}
              
              {/* Función auxiliar para verificar si el estado de suscripción coincide con algún valor */}
              {(function() {
                // Verificamos si debemos mostrar el botón de suscripción
                // Usamos 'as any' para evitar errores de tipo en la comparación
                const status = subscriptionStatus as any;
                const needsToSubscribe = (
                  // Si está en cualquiera de estos estados o es null
                  (status === 'inactive' || 
                   status === 'cancelled' || 
                   status === 'suspended' || 
                   status === null) && 
                  // Y no está suspendida ni cancelada por UI
                  !isSuspended && !isCancelled
                );
                
                return needsToSubscribe;
              })() && (
                <button
                  onClick={() => {
                    // Redirigir directamente a Paddle con el email del usuario
                    console.log('👤 Usuario actual:', {
                      email: user?.email,
                      uid: user?.uid,
                      displayName: user?.displayName
                    });

                    if (!user?.email) {
                      console.error('❌ No hay email del usuario:', user);
                      toast.error('Error: No se pudo obtener el email del usuario');
                      return;
                    }

                    // Generar URL de Paddle según el ciclo de facturación
                    const paddleUrl = billingCycle === 'monthly'
                      ? `https://pay.paddle.io/hsc_01jypp2mn7q86qmr1a1qmgbnrq_2jedg02th2hwy2r3apg661spag5jkjnh?theme=dark&user_email=${encodeURIComponent(user.email)}`
                      : `https://pay.paddle.io/hsc_01jz1jhbs3dn1p4nd8rrqw0z6g_9gc65ak5b07ygbk7azwrb1egrjxzzb6p?theme=dark&user_email=${encodeURIComponent(user.email)}`;

                    console.log('🔗 URL de Paddle generada:', paddleUrl);
                    console.log('📧 Email enviado a Paddle:', user.email);
                    window.open(paddleUrl, '_blank');
                  }}
                  className="w-full py-3 px-6 bg-black hover:bg-gray-800 text-white rounded-xl font-medium shadow-lg transition-all duration-200 flex items-center justify-center hover:shadow-xl transform hover:-translate-y-0.5"
                >
                  <SparklesIcon className="h-5 w-5 mr-2" />
                  Comenzar Ahora
                </button>
              )}

              {/* CASO 2: Suscripción activa normal - Mostrar botón para suspender */}
              {(function() {
                // Verificamos si podemos mostrar el botón para suspender
                return (
                  // Solo si es activa y no está suspendida ni cancelada
                  (subscriptionStatus as string) === 'active' && 
                  !isSuspended && 
                  !isCancelled && 
                  !debugInfo.firestoreData?.subscriptionSuspended
                );
              })() && (
                <button
                  onClick={() => setShowCancelModal(true)}
                  className="w-full py-3 px-6 bg-red-50 hover:bg-red-100 text-red-700 rounded-xl font-medium transition-colors border border-red-200"
                >
                  Gestionar suscripción
                </button>
              )}

              {/* Suscripción suspendida - Mostrar botón para reactivar */}
              {isSuspended && (
                <div className="space-y-3">
                  <button
                    onClick={handleReactivate}
                    disabled={isLoading}
                    className="w-full py-3 px-6 bg-green-600 hover:bg-green-700 text-white rounded-xl font-medium shadow-lg transition-all duration-200 disabled:bg-green-300 flex items-center justify-center"
                  >
                    {isLoading ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Procesando...
                      </>
                    ) : (
                      <>
                        <ArrowPathIcon className="h-5 w-5 mr-2" />
                        Actualizar método de pago
                      </>
                    )}
                  </button>
                  
                  {/* Mensaje informativo específico para usuarios Reveniu */}
                  {paymentProvider === 'reveniu' && (
                    <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg border border-blue-200">
                      <p className="font-medium">⚠️ Importante:</p>
                      <p>Al hacer clic en este botón, el sistema detectará automáticamente si tu suscripción:</p>
                      <ul className="list-disc pl-5 mt-2">
                        <li><span className="font-medium">Está dentro del período de pago:</span> Se reactivará inmediatamente.</li>
                        <li><span className="font-medium">Está fuera del período de pago:</span> Se enviará un enlace a tu correo electrónico para actualizar el método de pago.</li>
                      </ul>
                    </div>
                  )}
                </div>
              )}

              {/* Suscripción cancelada - Mostrar botón para reactivar en lugar de renovar */}
              {isCancelled && !isSuspended && (
                <button
                  onClick={handleReactivate}
                  disabled={isLoading}
                  className="w-full py-3 px-6 bg-blue-600 hover:bg-blue-700 text-white rounded-xl font-medium shadow-lg transition-all duration-200 flex items-center justify-center"
                >
                  <ArrowPathIcon className="h-5 w-5 mr-2" />
                  Reactivar suscripción
                </button>
              )}
            </div>
          </div>
        </div>
          </>
        ) : (
          // Usuario PRO - Mostrar tabla detallada de suscripción
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
              {/* Header de la tabla */}
              <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-8 py-6 border-b border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-xl font-bold text-gray-900">Mi Suscripción</h3>
                    <p className="text-gray-600 text-sm mt-1">Detalles de tu plan actual</p>
                  </div>
                  <div className="bg-black rounded-xl p-3">
                    <SparklesIcon className="h-6 w-6 text-white" />
                  </div>
                </div>
              </div>

              {/* Contenido de la tabla */}
              <div className="px-8 py-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Información del plan */}
                  <div className="space-y-6">
                    <div>
                      <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">Plan Actual</label>
                      <p className="text-lg font-semibold text-gray-900 mt-1">
                        {/* Usar las funciones helper para determinar el plan */}
                        {(() => {
                          const planType = getCurrentPlanType();
                          const cycle = getCurrentBillingCycle() === 'monthly' ? 'Mensual' : 'Anual';
                          return `Plan ${planType} ${cycle}`;
                        })()}
                      </p>
                    </div>

                    <div>
                      <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">Estado</label>
                      <div className="flex items-center mt-1">
                        {isSuspended ? (
                          <>
                            <div className="bg-amber-100 rounded-full p-1 mr-2">
                              <ExclamationTriangleIcon className="h-4 w-4 text-amber-600" />
                            </div>
                            <span className="text-amber-700 font-medium">Suspendida</span>
                          </>
                        ) : (
                          <>
                            <div className="bg-green-100 rounded-full p-1 mr-2">
                              <CheckIcon className="h-4 w-4 text-green-600" />
                            </div>
                            <span className="text-green-700 font-medium">Activa</span>
                          </>
                        )}
                      </div>
                    </div>

                    {paymentMethod && (
                      <div>
                        <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">Método de Pago</label>
                        <p className="text-lg font-semibold text-gray-900 mt-1">{paymentMethod}</p>
                      </div>
                    )}
                  </div>

                  {/* Información de facturación */}
                  <div className="space-y-6">
                    {!isSuspended && nextRenewalDate && (
                      <div>
                        <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">Próximo Pago</label>
                        <p className="text-lg font-semibold text-gray-900 mt-1">{nextRenewalDate}</p>
                      </div>
                    )}

                    <div>
                      <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">Precio</label>
                      <p className="text-lg font-semibold text-gray-900 mt-1">
                        {getCurrentBillingCycle() === 'monthly' ? '$50/mes' : '$480/año'}
                      </p>
                    </div>

                    {isSuspended && endDate && (
                      <div>
                        <label className="text-sm font-medium text-gray-500 uppercase tracking-wide">Acceso Hasta</label>
                        <p className="text-lg font-semibold text-amber-700 mt-1">{endDate}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Footer con botones de acción */}
              <div className="bg-gray-50 px-8 py-6 border-t border-gray-200">
                {!isSuspended ? (
                  // Suscripción activa - Mostrar información y botón suspender
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div className="mb-4 sm:mb-0">
                      <p className="text-sm text-gray-600">
                        Tu suscripción se renovará automáticamente el {nextRenewalDate}
                      </p>
                    </div>
                    <button
                      onClick={() => setShowCancelModal(true)}
                      className="inline-flex items-center px-4 py-2 border border-red-300 rounded-lg text-sm font-medium text-red-700 bg-white hover:bg-red-50 transition-colors"
                    >
                      Gestionar Suscripción
                    </button>
                  </div>
                ) : (
                  // Suscripción suspendida - Mostrar mensaje y botón reactivar
                  <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div className="mb-4 sm:mb-0">
                      <p className="text-sm text-amber-700 font-medium">
                        Tu suscripción está suspendida. Tendrás acceso hasta el {endDate}.
                      </p>
                      <p className="text-xs text-gray-600 mt-1">
                        Después de esta fecha, tu cuenta volverá al plan Tatuador Independiente.
                      </p>
                    </div>
                    <button
                      onClick={handleReactivate}
                      disabled={isLoading}
                      className="inline-flex items-center px-6 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg font-medium shadow-sm transition-colors disabled:bg-green-300"
                    >
                      {isLoading ? (
                        <>
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          Procesando...
                        </>
                      ) : (
                        'Actualizar método de pago'
                      )}
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Mantener la sección de estado solo para referencia (oculta) */}
        {false && (userIsPro || isSuspended || isCancelled) && (
          <div className="mt-12 max-w-3xl mx-auto">
            <div className="bg-white rounded-2xl border border-gray-200 shadow-lg overflow-hidden">
              <div className="px-8 py-6 border-b border-gray-200">
                <h3 className="text-xl font-bold text-gray-900">Estado de tu suscripción</h3>
              </div>

              <div className="px-8 py-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center">
                    {isSuspended ? (
                      <div className="flex items-center">
                        <div className="bg-amber-100 rounded-full p-2 mr-3">
                          <ExclamationTriangleIcon className="h-5 w-5 text-amber-600" />
                        </div>
                        <div>
                          <span className="font-semibold text-amber-700">Suscripción Suspendida</span>
                          <p className="text-sm text-gray-600">Tu suscripción está pausada temporalmente</p>
                        </div>
                      </div>
                    ) : isScheduledForCancellation ? (
                      <div className="flex items-center">
                        <div className="bg-orange-100 rounded-full p-2 mr-3">
                          <ClockIcon className="h-5 w-5 text-orange-600" />
                        </div>
                        <div>
                          <span className="font-semibold text-orange-700">Cancelación Programada</span>
                          <p className="text-sm text-gray-600">Activo hasta {scheduledCancellationDate}</p>
                        </div>
                      </div>
                    ) : isCancelled ? (
                      <div className="flex items-center">
                        <div className="bg-blue-100 rounded-full p-2 mr-3">
                          <ClockIcon className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <span className="font-semibold text-blue-700">Suscripción Cancelada</span>
                          <p className="text-sm text-gray-600">Acceso hasta {endDate}</p>
                        </div>
                      </div>
                    ) : (
                      <div className="flex items-center">
                        <div className="bg-green-100 rounded-full p-2 mr-3">
                          <CheckIcon className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <span className="font-semibold text-green-700">Suscripción Activa</span>
                          <p className="text-sm text-gray-600">Plan Artista del Tatuaje</p>
                        </div>
                      </div>
                    )}
                  </div>

                  {paymentMethod && !isCancelled && (
                    <span className="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm font-medium">
                      {paymentMethod}
                    </span>
                  )}
                </div>

                {/* Información adicional según el estado */}
                <div className="bg-gray-50 rounded-xl p-4">
                  <p className="text-gray-700 text-sm">
                    {isSuspended
                      ? 'Puedes reactivar tu suscripción en cualquier momento para recuperar todas las funcionalidades premium.'
                      : isScheduledForCancellation
                        ? `Tu suscripción está programada para cancelarse el ${scheduledCancellationDate}. Hasta esa fecha, mantienes acceso completo a todas las funcionalidades premium. Puedes reactivar tu suscripción desde el portal de cliente.`
                        : isCancelled
                          ? `Podrás seguir utilizando todas las funcionalidades premium sin restricciones hasta el ${endDate}. Después de esta fecha, tu cuenta volverá automáticamente al plan Tatuador Independiente.`
                          : 'Tienes acceso completo a todas las funcionalidades premium de Tatu.ink sin limitaciones.'}
                  </p>

                  {/* Información de renovación */}
                  {!isCancelled && !isSuspended && !isScheduledForCancellation && nextRenewalDate && (
                    <div className="mt-3 flex items-center text-sm text-gray-600">
                      <ClockIcon className="h-4 w-4 mr-2" />
                      <span>Próxima renovación: <strong>{nextRenewalDate}</strong></span>
                    </div>
                  )}

                  {/* Cuenta regresiva para cancelación programada */}
                  {isScheduledForCancellation && scheduledCancellationDate && (
                    <div className="mt-3 flex items-center text-sm text-orange-600">
                      <ClockIcon className="h-4 w-4 mr-2" />
                      <span>
                        {(function() {
                          if (!scheduledCancellationDate) return "Información no disponible";

                          const endDateObj = new Date(scheduledCancellationDate!.split('/').reverse().join('-'));
                          const now = new Date();
                          const diffTime = endDateObj.getTime() - now.getTime();
                          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                          if (diffDays <= 0) {
                            return "La cancelación está programada para hoy";
                          } else if (diffDays === 1) {
                            return "La suscripción se cancelará mañana";
                          } else {
                            return `La suscripción se cancelará en ${diffDays} días`;
                          }
                        })()}
                      </span>
                    </div>
                  )}

                  {/* Cuenta regresiva para suscripciones canceladas */}
                  {isCancelled && endDate && (
                    <div className="mt-3 flex items-center text-sm text-blue-600">
                      <ClockIcon className="h-4 w-4 mr-2" />
                      <span>
                        {(function() {
                          // Verificar que endDate no sea null antes de intentar crear una fecha
                          if (!endDate) return "Información no disponible";
                          
                          // Crear una copia segura de la fecha
                          const endDateString = String(endDate);
                          const endDateObj = new Date(endDateString);
                          const now = new Date();
                          const diffTime = endDateObj.getTime() - now.getTime();
                          const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

                          if (diffDays <= 0) {
                            return "Tu acceso premium ha expirado";
                          } else if (diffDays === 1) {
                            return "Te queda 1 día de acceso premium";
                          } else {
                            return `Te quedan ${diffDays} días de acceso premium`;
                          }
                        })()}
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}


      </div>



      {/* Modal de cancelación de suscripción - Diseño moderno */}
      {showCancelModal && (
        <ModalPortal>
          <div
            className="fixed inset-0 bg-gray-900 bg-opacity-75 backdrop-blur-sm flex items-center justify-center p-4"
            onClick={() => setShowCancelModal(false)}
          >
            <div
              className="relative transform overflow-hidden rounded-2xl bg-white text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="absolute right-0 top-0 pr-4 pt-4 z-10">
                <button
                  type="button"
                  className="rounded-full bg-white/10 backdrop-blur-sm text-white hover:text-gray-200 focus:outline-none p-1.5"
                  onClick={() => setShowCancelModal(false)}
                >
                  <span className="sr-only">Cerrar</span>
                  <XMarkIcon className="h-5 w-5" aria-hidden="true" />
                </button>
              </div>

              <div className="bg-gradient-to-r from-red-600 to-red-500 px-6 py-6 text-white">
                <div className="flex items-center">
                  <div className="flex-shrink-0 bg-white/20 backdrop-blur-sm rounded-full p-2">
                    <ExclamationTriangleIcon className="h-6 w-6 text-white" aria-hidden="true" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-xl font-bold">Gestionar suscripción</h3>
                    <p className="mt-1 text-red-100">
                      Accede al portal de Paddle para gestionar tu suscripción
                    </p>
                  </div>
                </div>
              </div>

              <div className="px-6 py-6">
                <p className="text-gray-700">
                  Se abrirá el portal de gestión de Paddle donde podrás cancelar, pausar o modificar tu suscripción de forma segura.
                </p>

                {/* Mensaje informativo sobre el portal de Paddle */}
                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <ShieldCheckIcon className="h-5 w-5 text-blue-500" />
                    </div>
                    <div className="ml-3">
                      <p className="text-sm text-blue-700">
                        <span className="font-semibold">Portal seguro de Paddle:</span> Serás redirigido al portal oficial de Paddle donde podrás gestionar tu suscripción de forma segura. Allí podrás cancelar, pausar, cambiar el método de pago o modificar tu plan.
                      </p>
                    </div>
                  </div>
                </div>

                {cancelError && (
                  <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                    <div className="flex">
                      <div className="flex-shrink-0">
                        <ExclamationTriangleIcon className="h-5 w-5 text-red-400" />
                      </div>
                      <div className="ml-3">
                        <p className="text-sm text-red-700">{cancelError}</p>
                      </div>
                    </div>
                  </div>
                )}

                <div className="mt-6 flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-3">
                  <button
                    type="button"
                    className="mt-3 sm:mt-0 inline-flex justify-center items-center rounded-lg border border-gray-300 bg-white px-4 py-2.5 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none transition-colors"
                    onClick={() => setShowCancelModal(false)}
                  >
                    Volver
                  </button>
                  <button
                    type="button"
                    className="inline-flex justify-center items-center rounded-lg border border-transparent bg-red-600 px-4 py-2.5 text-sm font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none transition-colors disabled:bg-red-400"
                    onClick={handleCancelSubscription}
                    disabled={isCancelling}
                  >
                    {isCancelling ? (
                      <>
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Procesando...
                      </>
                    ) : 'Abrir Portal de Gestión'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </ModalPortal>
      )}
    </div>
  );
}
